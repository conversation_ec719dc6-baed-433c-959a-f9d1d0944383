<?php
/**
 * Choose Stake API
 * 
 * This API handles stake operations for the game:
 * - Checking user balance
 * - Subtracting stake amount from user balance
 * - Redirecting to race scene
 * 
 * All operations require JWT token authentication
 */

// Enable error reporting for debugging
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Log errors to a file
ini_set('log_errors', 1);
ini_set('error_log', 'choose_stake_errors.log');

require_once 'inc/db.php';
require_once 'inc/security.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Helper function to safely log debug info
function debug_log($message) {
    // Only log errors and important warnings
    if (strpos(strtolower($message), 'error') !== false || 
        strpos(strtolower($message), 'fail') !== false ||
        strpos(strtolower($message), 'critical') !== false ||
        strpos(strtolower($message), 'exception') !== false) {
        error_log(date('Y-m-d H:i:s') . " - " . $message . "\n", 3, 'choose_stake_debug.log');
    }
}

// Start debugging - disabled regular logging
// debug_log("API request received: " . json_encode($_POST));

// Check if this is a test request
if (isset($_GET['test']) && $_GET['test'] == '1') {
    debug_log("Test request received");
    echo json_encode([
        'status' => 'success',
        'message' => 'API is working',
        'time' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    respondJSON(['status' => 'error', 'message' => 'Method not allowed'], 405);
}

// Check if getBearerToken function exists
if (!function_exists('getBearerToken')) {
    function getBearerToken() {
        $headers = null;
        if (isset($_SERVER['Authorization'])) {
            $headers = trim($_SERVER['Authorization']);
        } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
        } elseif (function_exists('apache_request_headers')) {
            $requestHeaders = apache_request_headers();
            $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
            if (isset($requestHeaders['Authorization'])) {
                $headers = trim($requestHeaders['Authorization']);
            }
        }

        if (!empty($headers) && preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
        return null;
    }
    
    debug_log("Created getBearerToken function");
}

// Get authorization token
$token = getBearerToken();
debug_log("Token received: " . ($token ? "Yes (length: " . strlen($token) . ")" : "No"));

if (!$token) {
    debug_log("ERROR: No authorization token provided");
    respondJSON(['status' => 'error', 'message' => 'No authorization token provided'], 401);
}

// Log the first few characters of the token for debugging (not the full token for security)
debug_log("Token starts with: " . substr($token, 0, 20) . "...");

// Verify token
$user_id = verifyJWT($token);
debug_log("User ID after verification: " . (is_scalar($user_id) ? $user_id : (is_array($user_id) ? "Array" : "Invalid")));

if (!$user_id) {
    debug_log("ERROR: JWT verification failed for token");
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired token'], 401);
}

// If somehow user_id is still an array, extract the ID (this should be fixed in security.php but adding as a fallback)
if (is_array($user_id) && isset($user_id['user_id'])) {
    $user_id = $user_id['user_id'];
    debug_log("Extracted user_id from array: " . $user_id);
}

// Check rate limit
if (!checkRateLimit($user_id)) {
    respondJSON(['status' => 'error', 'message' => 'Rate limit exceeded. Please try again later.'], 429);
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

debug_log("Received input: " . json_encode($input));

// Validate required fields - key1 is required, nonce is optional with 24-hour token system
if (!isset($input['key1'])) {
    respondJSON(['status' => 'error', 'message' => 'Key1 is required'], 400);
}

$key1 = $input['key1'];
$nonce = isset($input['nonce']) ? $input['nonce'] : null;
$operation = isset($input['operation']) ? $input['operation'] : '';
$stake_amount = isset($input['stake_amount']) ? $input['stake_amount'] : 0;

debug_log("Operation: $operation, Stake Amount: $stake_amount");

// Verify Key1
if (!verifyUserKey($user_id, $key1)) {
    respondJSON(['status' => 'error', 'message' => 'Invalid or expired Key1'], 401);
}

// Verify Nonce (only if provided - optional with 24-hour token system)
if ($nonce !== null && !verifyAndUseNonce($user_id, $nonce)) {
    respondJSON(['status' => 'error', 'message' => 'Invalid or used nonce'], 401);
}

// Generate a new nonce for the next request
$new_nonce = generateNonce($user_id);
debug_log("New nonce generated: $new_nonce");

// Execute the requested operation
switch ($operation) {
    case 'FetchUserData':
        fetch_user_data($conn, $user_id, $new_nonce);
        break;

    case 'ChooseStake':
        if (empty($stake_amount) || !is_numeric($stake_amount)) {
            respondJSON([
                'status' => 'error',
                'message' => 'Valid stake amount is required',
                'nonce' => $new_nonce
            ], 400);
        }
        process_stake($conn, $user_id, $stake_amount, $new_nonce);
        break;

    case 'CheckUnusedStakes':
        check_unused_stakes($conn, $user_id, $new_nonce);
        break;

    default:
        respondJSON([
            'status' => 'error',
            'message' => 'Invalid operation',
            'nonce' => $new_nonce
        ], 400);
        break;
}

/**
 * Fetch user data including balance
 */
function fetch_user_data($conn, $user_id, $nonce) {
    debug_log("Fetching user data for user ID: $user_id");
    
    try {
        // Get user data
        $stmt = $conn->prepare("SELECT username, balance FROM users u WHERE u.id = ?");
                               
        if (!$stmt) {
            debug_log("SQL preparation error: " . $conn->error);
            respondJSON(['status' => 'error', 'message' => 'Database error', 'nonce' => $nonce], 500);
        }
        
        $stmt->bind_param('i', $user_id);
        $result = $stmt->execute();
        
        if (!$result) {
            debug_log("SQL execution error: " . $stmt->error);
            respondJSON(['status' => 'error', 'message' => 'Database error', 'nonce' => $nonce], 500);
        }
        
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            debug_log("User not found");
            respondJSON(['status' => 'error', 'message' => 'User not found', 'nonce' => $nonce], 404);
        }
        
        $user = $result->fetch_assoc();
        
        // Generate a unique encryption key for this session
        // ترکیبی از شناسه کاربر، زمان فعلی و یک مقدار تصادفی
        $unique_seed = $user_id . time() . bin2hex(random_bytes(8));
        $encryption_key = hash('sha256', $unique_seed);
        
        // تولید یک کلید کوتاه‌تر - 32 کاراکتر اول
        $short_encryption_key = substr($encryption_key, 0, 32);
        
        debug_log("Generated encryption key for user: $user_id");
        
        // Return user data with encryption key
        $response = [
            'status' => 'success',
            'user' => [
                'username' => $user['username'],
                'balance' => (float)$user['balance']
            ],
            'encryption_key' => $short_encryption_key,
            'nonce' => $nonce
        ];
        
        respondJSON($response);
        
    } catch (Exception $e) {
        debug_log("Exception in fetch_user_data: " . $e->getMessage());
        respondJSON([
            'status' => 'error', 
            'message' => 'Server error: ' . $e->getMessage(),
            'nonce' => $nonce
        ], 500);
    }
}

/**
 * Process the stake amount if user has enough balance
 */
function process_stake($conn, $user_id, $stake_amount, $nonce) {
    debug_log("Processing stake for user ID: $user_id, Stake Amount: $stake_amount");
    
    try {
        // Validate stake amount (must be one of 5, 10, 20, 50)
        $valid_stakes = [5, 10, 20, 50];
        if (!in_array((float)$stake_amount, $valid_stakes)) {
            respondJSON([
                'status' => 'error',
                'message' => 'Invalid stake amount. Must be one of: ' . implode(', ', $valid_stakes),
                'nonce' => $nonce
            ], 400);
        }
        
        // Get user balance
        $stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            respondJSON(['status' => 'error', 'message' => 'User not found', 'nonce' => $nonce], 404);
        }
        
        $user_data = $result->fetch_assoc();
        $balance = (float)$user_data['balance'];
        
        // Check if user has enough balance
        if ($balance < $stake_amount) {
            respondJSON([
                'status' => 'error',
                'message' => 'Not enough balance for this stake. You need '. $stake_amount .' but you have '. $balance .'.',
                'nonce' => $nonce
            ], 400);
        }
        
        // Check if race_tokens table exists
        $result = $conn->query("SHOW TABLES LIKE 'race_tokens'");
        if ($result->num_rows === 0) {
            // Create the race_tokens table if it doesn't exist
            $sql = "CREATE TABLE `race_tokens` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `token` varchar(255) NOT NULL,
                `stake_amount` decimal(10,2) NOT NULL,
                `used` tinyint(1) NOT NULL DEFAULT '0',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `token` (`token`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
            
            $conn->query($sql);
            debug_log("Created race_tokens table");
        }
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Deduct balance
            $new_balance = $balance - $stake_amount;
            $stmt = $conn->prepare("UPDATE users SET balance = ? WHERE id = ?");
            $stmt->bind_param('di', $new_balance, $user_id);
            $stmt->execute();
            
            // Generate a one-time race token
            $race_token = bin2hex(random_bytes(16));
            
            // Fix timezone issue - set timezone explicitly and ensure expiry is after creation
            date_default_timezone_set('Asia/Tehran'); // Set to your server's timezone
            $current_time = date('Y-m-d H:i:s');
            $expiry = date('Y-m-d H:i:s', strtotime($current_time . ' +24 hours')); // Changed from 5 minutes to 24 hours
            
            // Make sure MySQL timezone is also set correctly
            $conn->query("SET time_zone = '+03:30'"); // Tehran is UTC+3:30
            
            debug_log("Current time: $current_time, Expiry time: $expiry");
            
            // Clean up old or used tokens first
            $cleanup_stmt = $conn->prepare("DELETE FROM race_tokens WHERE user_id = ? AND (expires_at < NOW() OR used = 1)");
            $cleanup_stmt->bind_param("i", $user_id);
            $cleanup_stmt->execute();
            debug_log("Cleaned up old tokens for user $user_id");
            
            // Store the race token
            $stmt = $conn->prepare("INSERT INTO race_tokens (user_id, token, stake_amount, expires_at) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("isds", $user_id, $race_token, $stake_amount, $expiry);
            $result = $stmt->execute();
            
            if (!$result) {
                debug_log("Failed to insert race token: " . $stmt->error);
                throw new Exception("Failed to store race token");
            }
            
            debug_log("Successfully inserted race token for user $user_id: $race_token");
            
            // Debug: Verify the token was actually inserted
            $verify_stmt = $conn->prepare("SELECT * FROM race_tokens WHERE token = ?");
            $verify_stmt->bind_param("s", $race_token);
            $verify_stmt->execute();
            $verify_result = $verify_stmt->get_result();
            
            if ($verify_result->num_rows === 0) {
                debug_log("CRITICAL ERROR: Token was not inserted correctly!");
                throw new Exception("Token was not stored correctly in database");
            } else {
                debug_log("Token verification successful - token exists in database");
            }
            
            // بررسی اولین مسابقه برای کاربران رفرال شده
            // ابتدا بررسی می‌کنیم که آیا این کاربر از طریق رفرال آمده است یا خیر
            checkFirstRaceForReferredUser($conn, $user_id);
            
            // Commit transaction
            $conn->commit();
            
            // Return success response with race token
            respondJSON([
                'status' => 'success',
                'message' => 'Stake processed successfully',
                'balance' => $new_balance,
                'stake_amount' => (float)$stake_amount,
                'race_token' => $race_token,
                'nonce' => $nonce
            ]);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        debug_log("Exception in process_stake: " . $e->getMessage());
        respondJSON([
            'status' => 'error', 
            'message' => 'Server error: ' . $e->getMessage(),
            'nonce' => $nonce
        ], 500);
    }
}

/**
 * Check for unused stakes that are still valid
 */
function check_unused_stakes($conn, $user_id, $nonce) {
    debug_log("Checking unused stakes for user ID: $user_id");

    try {
        // Clean up expired tokens first
        $cleanup_stmt = $conn->prepare("DELETE FROM race_tokens WHERE user_id = ? AND expires_at < NOW()");
        $cleanup_stmt->bind_param("i", $user_id);
        $cleanup_stmt->execute();
        debug_log("Cleaned up expired tokens for user $user_id");

        // Get unused stakes that are still valid
        $stmt = $conn->prepare("
            SELECT stake_amount, token, expires_at, created_at
            FROM race_tokens
            WHERE user_id = ? AND used = 0 AND expires_at > NOW()
            ORDER BY created_at DESC
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        $unused_stakes = [];
        while ($row = $result->fetch_assoc()) {
            $unused_stakes[] = [
                'stake_amount' => (float)$row['stake_amount'],
                'token' => $row['token'],
                'expires_at' => $row['expires_at'],
                'created_at' => $row['created_at']
            ];
        }

        debug_log("Found " . count($unused_stakes) . " unused stakes for user $user_id");

        respondJSON([
            'status' => 'success',
            'unused_stakes' => $unused_stakes,
            'nonce' => $nonce
        ]);

    } catch (Exception $e) {
        debug_log("Exception in check_unused_stakes: " . $e->getMessage());
        respondJSON([
            'status' => 'error',
            'message' => 'Server error: ' . $e->getMessage(),
            'nonce' => $nonce
        ], 500);
    }
}

/**
 * بررسی اولین مسابقه برای کاربران رفرال شده و اعطای پاداش به دعوت‌کننده
 */
function checkFirstRaceForReferredUser($conn, $user_id) {
    try {
        // بررسی می‌کنیم که آیا این کاربر از طریق رفرال آمده است یا خیر
        $stmt = $conn->prepare("SELECT referrer_id, registration_rewarded, race_rewarded FROM referrals WHERE referred_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $referral_data = $result->fetch_assoc();
            $referrer_id = $referral_data['referrer_id'];
            $race_rewarded = $referral_data['race_rewarded'];
            
            // اگر قبلاً پاداش مسابقه داده نشده باشد
            if ($race_rewarded == 0) {
                // پاداش مرحله دوم (1.0 به موجودی رفرال) اضافه می‌شود
                $race_reward = 1.0;
                
                // بروزرسانی موجودی رفرال دعوت‌کننده
                $stmt = $conn->prepare("UPDATE users SET referral_balance = referral_balance + ? WHERE id = ?");
                $stmt->bind_param("di", $race_reward, $referrer_id);
                $stmt->execute();
                
                // بروزرسانی وضعیت پاداش در جدول referrals
                $stmt = $conn->prepare("UPDATE referrals SET race_rewarded = 1 WHERE referred_id = ?");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                
                // ثبت لاگ
                debug_log("Referral race reward added: User $referrer_id received $race_reward for first race of referred user $user_id");
            }
        }
    } catch (Exception $e) {
        // در صورت بروز خطا، فقط لاگ می‌کنیم و اجازه می‌دهیم تراکنش اصلی ادامه یابد
        debug_log("Error processing referral race reward: " . $e->getMessage());
    }
}
?> 