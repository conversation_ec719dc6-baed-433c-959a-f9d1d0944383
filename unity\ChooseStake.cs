using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using TMPro;
using UnityEngine.UI;
using System.Text;
using Newtonsoft.Json;
using UnityEngine.SceneManagement;
using System.Security.Cryptography;
using Newtonsoft.Json.Linq;

public class ChooseStake : MonoBehaviour
{
    [SerializeField] private Button stake5Button;
    [SerializeField] private Button stake10Button;
    [SerializeField] private Button stake20Button;
    [SerializeField] private Button stake50Button;
    [SerializeField] private Button nextButton; // Pay button
    [SerializeField] private Button startButton; // Start race button
    [SerializeField] private Button refreshButton; // Refresh data button
    [SerializeField] private TextMeshProUGUI statusText;
    [SerializeField] private TextMeshProUGUI balanceText;
    [SerializeField] private TextMeshProUGUI selectedStakeText;
    [SerializeField] private string apiUrl = "https://game-gofaster.com/gam/api/choose_stake.php";
    [SerializeField] private string userDataApiUrl = "https://game-gofaster.com/gam/api/get_user_data.php";
    [SerializeField] private GameObject sessionManagerPrefab;
    [SerializeField] private string raceSceneName = "Race";

    private float selectedStakeAmount = 0;

    // توکن مسابقه را در حافظه نگه می‌داریم (به جای فقط PlayerPrefs)
    private string currentRaceToken = "";

    // کلید رمزنگاری که از سرور دریافت می‌شود
    private string encryptionKey = "";

    // User data model
    [System.Serializable]
    public class UserData
    {
        public string username;
        public float balance;
    }

    [System.Serializable]
    public class UserDataResponse
    {
        public string status;
        public string message;
        public UserData user;
        public string encryption_key;
        public string nonce;
        public int token_expires_in; // 24 hours in seconds
        public string token_type; // "Bearer"
    }

    [System.Serializable]
    public class StakeResponse
    {
        public string status;
        public string message;
        public float balance;
        public float stake_amount;
        public string race_token;
        public string nonce;
        public int token_expires_in; // 24 hours in seconds
        public string token_type; // "Bearer"
    }

    // Operation types for the API
    private enum ApiOperation
    {
        FetchUserData,
        ChooseStake,
        CheckUnusedStakes
    }

    [System.Serializable]
    private class StakeRequestData
    {
        public string stake_amount;
    }

    [System.Serializable]
    public class UnusedStake
    {
        public float stake_amount;
        public string token;
        public string expires_at;
        public string created_at;
    }

    [System.Serializable]
    public class UnusedStakesResponse
    {
        public string status;
        public string message;
        public UnusedStake[] unused_stakes;
        public string nonce;
    }

    private void Awake()
    {
        // Ensure SessionManager exists
        if (SessionManager.Instance == null && sessionManagerPrefab != null)
        {
            Instantiate(sessionManagerPrefab);
            Debug.Log("SessionManager instantiated from ChooseStake");
        }
    }

    private void Start()
    {
        // Check UI components
        CheckUIComponents();

        // Check session status
        if (SessionManager.Instance == null)
        {
            Debug.LogWarning("SessionManager is not found! Authentication might not work properly.");
        }
        else
        {
            Debug.Log($"SessionManager found. IsLoggedIn: {SessionManager.Instance.IsLoggedIn}");
        }

        // Add listeners to stake buttons
        if (stake5Button != null)
            stake5Button.onClick.AddListener(() => OnStakeButtonClick(5));

        if (stake10Button != null)
            stake10Button.onClick.AddListener(() => OnStakeButtonClick(10));

        if (stake20Button != null)
            stake20Button.onClick.AddListener(() => OnStakeButtonClick(20));

        if (stake50Button != null)
            stake50Button.onClick.AddListener(() => OnStakeButtonClick(50));

        // Add listener to next button (Pay)
        if (nextButton != null)
            nextButton.onClick.AddListener(OnPayButtonClick);

        // Add listener to start button
        if (startButton != null)
            startButton.onClick.AddListener(OnStartButtonClick);

        // Add listener to refresh button
        if (refreshButton != null)
            refreshButton.onClick.AddListener(OnRefreshButtonClick);

        // Initialize UI - make sure buttons are properly set
        SetButtonStates(false, false); // Both buttons disabled initially

        if (selectedStakeText != null)
            selectedStakeText.text = "Select a stake amount";

        // Fetch user data to update the balance display
        FetchUserData();

        // Check for unused stakes after user data is fetched (to use updated nonce)
        // CheckUnusedStakes(); // This will be called after FetchUserData completes
    }

    private void CheckUIComponents()
    {
        // Log UI component status for debugging
        if (stake5Button == null) Debug.LogError("stake5Button is not assigned in the Inspector!");
        if (stake10Button == null) Debug.LogError("stake10Button is not assigned in the Inspector!");
        if (stake20Button == null) Debug.LogError("stake20Button is not assigned in the Inspector!");
        if (stake50Button == null) Debug.LogError("stake50Button is not assigned in the Inspector!");
        if (nextButton == null) Debug.LogError("nextButton is not assigned in the Inspector!");
        if (startButton == null) Debug.LogError("startButton is not assigned in the Inspector!");
        if (refreshButton == null) Debug.LogError("refreshButton is not assigned in the Inspector!");
        if (statusText == null) Debug.LogError("statusText is not assigned in the Inspector!");
        if (balanceText == null) Debug.LogError("balanceText is not assigned in the Inspector!");
        if (selectedStakeText == null) Debug.LogError("selectedStakeText is not assigned in the Inspector!");

        Debug.Log("ChooseStake UI components check: " +
                 $"stake5Button: {(stake5Button != null ? "OK" : "NULL")}, " +
                 $"stake10Button: {(stake10Button != null ? "OK" : "NULL")}, " +
                 $"stake20Button: {(stake20Button != null ? "OK" : "NULL")}, " +
                 $"stake50Button: {(stake50Button != null ? "OK" : "NULL")}, " +
                 $"nextButton: {(nextButton != null ? "OK" : "NULL")}, " +
                 $"startButton: {(startButton != null ? "OK" : "NULL")}, " +
                 $"refreshButton: {(refreshButton != null ? "OK" : "NULL")}, " +
                 $"statusText: {(statusText != null ? "OK" : "NULL")}, " +
                 $"balanceText: {(balanceText != null ? "OK" : "NULL")}, " +
                 $"selectedStakeText: {(selectedStakeText != null ? "OK" : "NULL")}");
    }

    public void OnStakeButtonClick(float amount)
    {
        Debug.Log($"OnStakeButtonClick called with amount: {amount}");

        // Clear any existing unused stake data
        ClearUnusedStakeData();

        selectedStakeAmount = amount;

        if (statusText != null)
            statusText.text = "";

        if (selectedStakeText != null)
            selectedStakeText.text = $"Selected stake: ${selectedStakeAmount}";

        // Enable pay button, keep start button disabled
        SetButtonStates(true, false);
        Debug.Log("Button states updated after stake selection");

        // Highlight the selected button
        HighlightSelectedButton(amount);
    }

    private void ClearUnusedStakeData()
    {
        // Clear stored race token and stake data
        PlayerPrefs.DeleteKey("RaceToken");
        PlayerPrefs.DeleteKey("EncryptedRaceToken");

        if (SessionManager.Instance != null)
        {
            SessionManager.Instance.SetSessionValue("RaceToken", "");

            // Re-enable stake buttons based on balance
            string balanceStr = SessionManager.Instance.GetSessionValue("Balance");
            if (!string.IsNullOrEmpty(balanceStr) && float.TryParse(balanceStr, out float balance))
            {
                EnableStakeButtons(balance);
            }
        }

        Debug.Log("Cleared unused stake data and re-enabled stake buttons");
    }

    private string FormatExpiryTime(string expiresAt)
    {
        try
        {
            // Parse the server time (assuming it's in UTC or server timezone)
            DateTime expiryTime = DateTime.Parse(expiresAt);

            // Calculate time remaining
            TimeSpan timeRemaining = expiryTime - DateTime.Now;

            if (timeRemaining.TotalMinutes < 0)
            {
                return "Expired";
            }
            else if (timeRemaining.TotalHours >= 1)
            {
                return $"{(int)timeRemaining.TotalHours}h {timeRemaining.Minutes}m";
            }
            else
            {
                return $"{(int)timeRemaining.TotalMinutes}m";
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Error parsing expiry time: {e.Message}");
            return expiresAt; // Return original string if parsing fails
        }
    }

    private void HighlightSelectedButton(float amount)
    {
        // Reset all buttons to normal state
        ColorBlock normalColors = stake5Button.colors;

        if (stake5Button != null)
            stake5Button.colors = normalColors;
        if (stake10Button != null)
            stake10Button.colors = normalColors;
        if (stake20Button != null)
            stake20Button.colors = normalColors;
        if (stake50Button != null)
            stake50Button.colors = normalColors;

        // Highlight the selected button
        ColorBlock selectedColors = normalColors;
        selectedColors.normalColor = new Color(0.8f, 0.8f, 1f);
        selectedColors.highlightedColor = new Color(0.9f, 0.9f, 1f);

        switch (amount)
        {
            case 5:
                if (stake5Button != null)
                    stake5Button.colors = selectedColors;
                break;
            case 10:
                if (stake10Button != null)
                    stake10Button.colors = selectedColors;
                break;
            case 20:
                if (stake20Button != null)
                    stake20Button.colors = selectedColors;
                break;
            case 50:
                if (stake50Button != null)
                    stake50Button.colors = selectedColors;
                break;
        }
    }

    // Pay button - Process the stake
    public void OnPayButtonClick()
    {
        Debug.Log("OnPayButtonClick called");

        if (selectedStakeAmount <= 0)
        {
            if (statusText != null)
                statusText.text = "Please select a stake amount first";
            return;
        }

        // Disable pay button while processing
        SetButtonStates(false, false);
        Debug.Log("Both buttons disabled during payment processing");

        // Process the stake
        StartCoroutine(ProcessStakeCoroutine());
    }

    // Start button - Load the race scene
    public void OnStartButtonClick()
    {
        Debug.Log("OnStartButtonClick called - Starting race with stake amount: " + selectedStakeAmount);

        // Disable both buttons during scene loading
        SetButtonStates(false, false);
        Debug.Log("Both buttons disabled during scene loading");

        StartCoroutine(LoadRaceSceneAfterDelay(0.5f));
    }

    // Refresh button - Refresh all data like Start method
    public void OnRefreshButtonClick()
    {
        Debug.Log("OnRefreshButtonClick called - Refreshing all data");

        if (statusText != null)
            statusText.text = "Refreshing data...";

        // Clear any existing unused stake data
        ClearUnusedStakeData();

        // Reset selected stake
        selectedStakeAmount = 0;
        if (selectedStakeText != null)
            selectedStakeText.text = "Select a stake amount";

        // Reset button states
        SetButtonStates(false, false);

        // Reset button highlights
        HighlightSelectedButton(0); // This will reset all buttons to normal state

        // Fetch fresh user data
        FetchUserData();
        // CheckUnusedStakes will be called automatically after FetchUserData completes
    }

    public void FetchUserData()
    {
        if (statusText != null)
            statusText.text = "Fetching user data...";

        StartCoroutine(GetUserDataCoroutine());
    }

    public void CheckUnusedStakes()
    {
        StartCoroutine(CheckUnusedStakesCoroutine());
    }

    private IEnumerator GetUserDataCoroutine()
    {
        yield return StartCoroutine(ApiRequestCoroutine(ApiOperation.FetchUserData));
    }

    private IEnumerator CheckUnusedStakesCoroutine()
    {
        yield return StartCoroutine(ApiRequestCoroutine(ApiOperation.CheckUnusedStakes));
    }

    private IEnumerator ProcessStakeCoroutine()
    {
        if (statusText != null)
            statusText.text = "Processing stake...";

        if (SessionManager.Instance == null || !SessionManager.Instance.IsLoggedIn)
        {
            if (statusText != null)
                statusText.text = "Error: You must be logged in";

            Debug.LogError("Cannot process stake: SessionManager not found or user not logged in");
            yield break;
        }

        if (selectedStakeAmount <= 0)
        {
            if (statusText != null)
                statusText.text = "Error: Please select a stake amount";

            Debug.LogError("Invalid stake amount: " + selectedStakeAmount);
            yield break;
        }

        Debug.Log("Using ChooseStakeCoroutine for stake processing with amount: " + selectedStakeAmount);
        yield return StartCoroutine(ChooseStakeCoroutine(selectedStakeAmount));
    }

    private IEnumerator ApiRequestCoroutine(ApiOperation operation)
    {
        // Check if SessionManager exists and user is logged in
        if (SessionManager.Instance == null || !SessionManager.Instance.IsLoggedIn)
        {
            if (statusText != null)
                statusText.text = "Error: Not logged in";

            Debug.LogError("SessionManager not found or user not logged in");
            yield break;
        }

        // Get auth token, key1, and nonce from SessionManager
        string token = SessionManager.Instance.JWT_Token;
        string key1 = SessionManager.Instance.Key1;
        string nonce = SessionManager.Instance.CurrentNonce;

        // Debug authentication data
        Debug.Log($"Authentication data - Token length: {token?.Length ?? 0}, Key1 length: {key1?.Length ?? 0}, Nonce length: {nonce?.Length ?? 0}");
        if (!string.IsNullOrEmpty(token))
            Debug.Log($"Token starts with: {token.Substring(0, Math.Min(20, token.Length))}...");
        if (!string.IsNullOrEmpty(key1))
            Debug.Log($"Key1 starts with: {key1.Substring(0, Math.Min(10, key1.Length))}...");
        if (!string.IsNullOrEmpty(nonce))
            Debug.Log($"Nonce: {nonce}");

        // With 24-hour token system, we might not have a nonce yet, but that's okay
        // The server will generate one for us if needed
        if (string.IsNullOrEmpty(nonce))
        {
            Debug.Log("No nonce available, but using 24-hour token system");
            // We'll proceed without a nonce, the server will handle it
        }

        // Check if we have token and key (nonce is optional with 24-hour token system)
        if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(key1))
        {
            if (statusText != null)
                statusText.text = "Error: Authentication data missing";

            Debug.LogError($"Missing authentication data. Token: {!string.IsNullOrEmpty(token)}, Key1: {!string.IsNullOrEmpty(key1)}");
            yield break;
        }

        Debug.Log($"Using 24-hour token system - Token length: {token.Length}, Key1 length: {key1.Length}");
        if (!string.IsNullOrEmpty(nonce))
        {
            Debug.Log($"Using nonce: {nonce}");
        }

        // Choose the appropriate API URL based on operation
        string baseUrl = (operation == ApiOperation.FetchUserData) ? userDataApiUrl : apiUrl;
        string url = baseUrl;

        // Handle different URL parameter requirements for each API
        if (operation == ApiOperation.FetchUserData)
        {
            // For get_user_data.php, append key1 and nonce as GET parameters
            // With 24-hour token system, nonce is optional - only add if not empty
            if (!string.IsNullOrEmpty(nonce) && nonce.Trim().Length > 0)
            {
                url = $"{baseUrl}?key1={UnityWebRequest.EscapeURL(key1)}&nonce={UnityWebRequest.EscapeURL(nonce)}";
                Debug.Log($"Adding nonce to FetchUserData URL: {nonce}");
            }
            else
            {
                // With 24-hour token system, we can make requests without a nonce
                url = $"{baseUrl}?key1={UnityWebRequest.EscapeURL(key1)}";
                Debug.Log("No valid nonce available for FetchUserData request - using 24-hour token system");
            }

            Debug.Log($"Sending {operation} request to {url}");

            // Create the web request
            UnityWebRequest request = new UnityWebRequest(url, "GET");
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            request.SetRequestHeader("Authorization", "Bearer " + token);

            // Send the request
            yield return request.SendWebRequest();

            // Handle response
            if (request.result != UnityWebRequest.Result.Success)
            {
                if (statusText != null)
                    statusText.text = "Error: " + request.error;

                Debug.LogError($"API request error: {request.error}");
                yield break;
            }

            string responseText = request.downloadHandler.text;
            Debug.Log($"API response: {responseText}");

            // Process the response
            ProcessUserDataResponse(responseText);
        }
        else if (operation == ApiOperation.CheckUnusedStakes)
        {
            // Create request data for checking unused stakes
            Dictionary<string, object> requestData = new Dictionary<string, object>
            {
                { "operation", "CheckUnusedStakes" },
                { "key1", key1 }
            };

            // With 24-hour token system, nonce is optional - only add if not empty
            if (!string.IsNullOrEmpty(nonce) && nonce.Trim().Length > 0)
            {
                requestData.Add("nonce", nonce);
                Debug.Log($"Adding nonce to CheckUnusedStakes request: {nonce}");
            }
            else
            {
                Debug.Log("No valid nonce available for CheckUnusedStakes request - using 24-hour token system");
            }

            // Convert request data to JSON
            string jsonData = JsonConvert.SerializeObject(requestData);
            Debug.Log($"Sending {operation} request to {url}: {jsonData}");

            // Create the web request
            UnityWebRequest request = new UnityWebRequest(url, "POST");
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            request.SetRequestHeader("Authorization", "Bearer " + token);

            // Send the request
            yield return request.SendWebRequest();

            // Handle response
            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"API request error: {request.error}");
                yield break;
            }

            string responseText = request.downloadHandler.text;
            Debug.Log($"API response: {responseText}");

            // Process the response
            ProcessUnusedStakesResponse(responseText);
        }
        else // ChooseStake
        {
            // Create request data
            Dictionary<string, object> requestData = new Dictionary<string, object>
            {
                { "operation", "ChooseStake" },
                { "stake_amount", selectedStakeAmount },
                { "key1", key1 }
            };

            // With 24-hour token system, nonce is optional - only add if not empty
            if (!string.IsNullOrEmpty(nonce) && nonce.Trim().Length > 0)
            {
                requestData.Add("nonce", nonce);
                Debug.Log($"Adding nonce to ChooseStake request: {nonce}");
            }
            else
            {
                Debug.Log("No valid nonce available for ChooseStake request - using 24-hour token system");
            }

            // Convert request data to JSON
            string jsonData = JsonConvert.SerializeObject(requestData);
            Debug.Log($"Sending {operation} request to {url}: {jsonData}");

            // Create the web request
            UnityWebRequest request = new UnityWebRequest(url, "POST");
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            request.SetRequestHeader("Authorization", "Bearer " + token);

            // Send the request
            yield return request.SendWebRequest();

            // Handle response
            if (request.result != UnityWebRequest.Result.Success)
            {
                if (statusText != null)
                    statusText.text = "Error: " + request.error;

                Debug.LogError($"API request error: {request.error}");
                yield break;
            }

            string responseText = request.downloadHandler.text;
            Debug.Log($"API response: {responseText}");

            // Process the response
            ProcessStakeResponse(responseText);
        }
    }

    private void ProcessUserDataResponse(string responseText)
    {
        try
        {
            // Try to parse the JSON response
            UserDataResponse response = JsonConvert.DeserializeObject<UserDataResponse>(responseText);

            // Check if response is valid
            if (response == null)
            {
                if (statusText != null)
                    statusText.text = "Error: Invalid response format";

                Debug.LogError("Invalid response format");
                return;
            }

            // Update Session Manager with new nonce
            if (SessionManager.Instance != null && !string.IsNullOrEmpty(response.nonce))
            {
                SessionManager.Instance.UpdateNonce(response.nonce);
                Debug.Log($"Updated nonce: {response.nonce}");
            }

            // ذخیره کلید رمزنگاری دریافت شده از سرور
            if (!string.IsNullOrEmpty(response.encryption_key))
            {
                encryptionKey = response.encryption_key;
                Debug.Log("Received encryption key from server");

                // ذخیره کلید در PlayerPrefs برای استفاده در RaceManager
                PlayerPrefs.SetString("TempEncryptionKey", encryptionKey);
            }
            else
            {
                Debug.LogWarning("No encryption key received from server");
            }

            // Check for success status
            if (response.status == "success" && response.user != null)
            {
                // Store balance in SessionManager for later use
                if (SessionManager.Instance != null)
                {
                    SessionManager.Instance.SetSessionValue("Balance", response.user.balance.ToString());
                }

                // Update UI
                if (balanceText != null)
                    balanceText.text = $"Balance: ${response.user.balance:F2}";

                if (statusText != null)
                    statusText.text = "User data retrieved successfully";

                Debug.Log("User data fetched successfully");

                // Enable or disable stake buttons based on balance
                UpdateStakeButtonStates(response.user.balance);

                // Now check for unused stakes with the updated nonce
                CheckUnusedStakes();
            }
            else
            {
                string errorMessage = response.message ?? "Unknown error";

                if (statusText != null)
                    statusText.text = "Error: " + errorMessage;

                Debug.LogError($"API error: {errorMessage}");
            }
        }
        catch (Exception e)
        {
            if (statusText != null)
                statusText.text = "Error parsing response";

            Debug.LogError($"Error parsing response: {e.Message}");
        }
    }

    private void ProcessUnusedStakesResponse(string responseText)
    {
        try
        {
            // Try to parse the JSON response
            UnusedStakesResponse response = JsonConvert.DeserializeObject<UnusedStakesResponse>(responseText);

            // Check if response is valid
            if (response == null)
            {
                Debug.LogError("Invalid unused stakes response format");
                return;
            }

            // Update Session Manager with new nonce
            if (SessionManager.Instance != null && !string.IsNullOrEmpty(response.nonce))
            {
                SessionManager.Instance.UpdateNonce(response.nonce);
                Debug.Log($"Updated nonce: {response.nonce}");
            }

            // Check for success status
            if (response.status == "success")
            {
                Debug.Log($"Found {response.unused_stakes?.Length ?? 0} unused stakes");

                // If there are unused stakes, handle them
                if (response.unused_stakes != null && response.unused_stakes.Length > 0)
                {
                    HandleUnusedStakes(response.unused_stakes);
                }
            }
            else
            {
                string errorMessage = response.message ?? "Unknown error";
                Debug.LogError($"Check unused stakes error: {errorMessage}");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Error parsing unused stakes response: {e.Message}");
        }
    }

    private void HandleUnusedStakes(UnusedStake[] unusedStakes)
    {
        // Get the most recent unused stake
        UnusedStake mostRecentStake = unusedStakes[0]; // Already ordered by created_at DESC

        Debug.Log($"Found unused stake: ${mostRecentStake.stake_amount} with token {mostRecentStake.token}");

        // Set the stake amount and enable start button
        selectedStakeAmount = mostRecentStake.stake_amount;

        // Update UI
        if (selectedStakeText != null)
            selectedStakeText.text = $"Unused stake found: ${selectedStakeAmount}";

        // Format expiry time for display
        string expiryDisplay = FormatExpiryTime(mostRecentStake.expires_at);

        if (statusText != null)
            statusText.text = $"Unused stake found! Expires: {expiryDisplay}. Click Start to use it.";

        // Highlight the corresponding stake button
        HighlightSelectedButton(selectedStakeAmount);

        // Disable only the specific stake button to prevent re-selection
        DisableSpecificStakeButton(selectedStakeAmount);

        // Enable start button, disable pay button
        SetButtonStates(false, true);

        // Store the race token for use
        PlayerPrefs.SetString("RaceToken", mostRecentStake.token);
        PlayerPrefs.SetFloat("SelectedStakeAmount", selectedStakeAmount);

        // Also store in SessionManager
        if (SessionManager.Instance != null)
        {
            SessionManager.Instance.SetSessionValue("RaceToken", mostRecentStake.token);
            Debug.Log("Unused stake token stored in SessionManager");
        }

        PlayerPrefs.Save();
        Debug.Log("Unused stake data stored for race");
    }

    private void DisableStakeButtons()
    {
        if (stake5Button != null)
            stake5Button.interactable = false;
        if (stake10Button != null)
            stake10Button.interactable = false;
        if (stake20Button != null)
            stake20Button.interactable = false;
        if (stake50Button != null)
            stake50Button.interactable = false;
    }

    private void DisableSpecificStakeButton(float stakeAmount)
    {
        switch (stakeAmount)
        {
            case 5:
                if (stake5Button != null)
                    stake5Button.interactable = false;
                break;
            case 10:
                if (stake10Button != null)
                    stake10Button.interactable = false;
                break;
            case 20:
                if (stake20Button != null)
                    stake20Button.interactable = false;
                break;
            case 50:
                if (stake50Button != null)
                    stake50Button.interactable = false;
                break;
        }
    }

    private void EnableStakeButtons(float balance)
    {
        if (stake5Button != null)
            stake5Button.interactable = (balance >= 5);
        if (stake10Button != null)
            stake10Button.interactable = (balance >= 10);
        if (stake20Button != null)
            stake20Button.interactable = (balance >= 20);
        if (stake50Button != null)
            stake50Button.interactable = (balance >= 50);
    }

    private void ProcessStakeResponse(string responseText)
    {
        try
        {
            JObject response = JObject.Parse(responseText);
            string status = response["status"]?.ToString();

            if (status == "success")
            {
                Debug.Log("Stake processed successfully");
                if (statusText != null)
                    statusText.text = "Stake successfully placed! Click Start to begin the race.";

                // Update UI to show stake was accepted - Force button state changes
                Debug.Log("Attempting to update button states after successful payment");

                // Use our helper method to ensure button states are set correctly
                SetButtonStates(false, true); // Disable pay button, enable start button

                // Add a delayed call to ensure UI updates properly
                StartCoroutine(DelayedButtonStateUpdate());

                // Get and store the new nonce from response if available
                if (response["nonce"] != null && SessionManager.Instance != null)
                {
                    string newNonce = response["nonce"].ToString();
                    Debug.Log("New nonce received: " + newNonce);
                    SessionManager.Instance.UpdateNonce(newNonce);
                }

                // Update balance information if available
                if (response["balance"] != null && balanceText != null)
                {
                    float newBalance = (float)response["balance"];
                    balanceText.text = "Balance: " + newBalance.ToString("F2");
                    Debug.Log("Updated balance: " + newBalance);
                }

                // Store the stake amount for the race scene
                PlayerPrefs.SetFloat("SelectedStakeAmount", selectedStakeAmount);
                Debug.Log("Stored stake amount: " + selectedStakeAmount);

                // Store race token if available
                if (response["race_token"] != null)
                {
                    string raceToken = response["race_token"].ToString();
                    Debug.Log("Race token received from server: " + raceToken);

                    // First, clear existing tokens to avoid confusion
                    PlayerPrefs.DeleteKey("RaceToken");
                    PlayerPrefs.DeleteKey("EncryptedRaceToken");

                    // Store token for verification
                    PlayerPrefs.SetString("RaceToken", raceToken);
                    Debug.Log("Race token stored in PlayerPrefs");

                    // Also store in SessionManager for easy access
                    if (SessionManager.Instance != null)
                    {
                        SessionManager.Instance.SetSessionValue("RaceToken", raceToken);
                        Debug.Log("Race token stored in SessionManager");
                    }

                    if (!string.IsNullOrEmpty(encryptionKey))
                    {
                        // Also store encrypted if encryption key available
                        SecurePlayerPrefsSet("EncryptedRaceToken", raceToken);
                        currentRaceToken = raceToken;
                        Debug.Log("Race token also stored encrypted");
                    }

                    PlayerPrefs.Save();
                }
                else
                {
                    Debug.LogError("ERROR: No race token in response! This will cause verification to fail.");
                }

                // Set DontDestroyOnLoad on SessionManager to ensure it persists between scenes
                if (SessionManager.Instance != null)
                {
                    DontDestroyOnLoad(SessionManager.Instance.gameObject);
                    Debug.Log("SessionManager set to persist between scenes");
                }
                else
                {
                    Debug.LogError("SessionManager not found!");
                }
            }
            else
            {
                string errorMessage = response["message"]?.ToString() ?? "Unknown error";
                Debug.LogError("Stake process error: " + errorMessage);
                if (statusText != null)
                    statusText.text = "Error: " + errorMessage;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("Failed to parse stake response: " + ex.Message);
            if (statusText != null)
                statusText.text = "Error: Failed to process response";
        }
    }

    // Delayed button state update to ensure UI refreshes properly
    private IEnumerator DelayedButtonStateUpdate()
    {
        // Wait for a frame to ensure UI updates
        yield return null;

        // Wait a bit more to be extra sure
        yield return new WaitForSeconds(0.1f);

        // Set button states again
        SetButtonStates(false, true);
        Debug.Log("Button states updated after delay");

        // Force UI refresh
        Canvas.ForceUpdateCanvases();
    }

    // Helper method to ensure button states are set correctly
    private void SetButtonStates(bool payButtonEnabled, bool startButtonEnabled)
    {
        Debug.Log($"SetButtonStates called with payButtonEnabled={payButtonEnabled}, startButtonEnabled={startButtonEnabled}");

        // Enable/disable the entire GameObject for each button
        if (nextButton != null)
        {
            // Enable/disable the GameObject
            nextButton.gameObject.SetActive(payButtonEnabled);

            Debug.Log($"Pay button GameObject active set to: {nextButton.gameObject.activeSelf}");
        }
        else
        {
            Debug.LogError("nextButton is null in SetButtonStates!");
        }

        if (startButton != null)
        {
            // Enable/disable the GameObject
            startButton.gameObject.SetActive(startButtonEnabled);

            Debug.Log($"Start button GameObject active set to: {startButton.gameObject.activeSelf}");
        }
        else
        {
            Debug.LogError("startButton is null in SetButtonStates!");
        }
    }

    private IEnumerator LoadRaceSceneAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);

        Debug.Log("Loading race scene: " + raceSceneName);

        // Make sure to save PlayerPrefs before scene transition
        if (!string.IsNullOrEmpty(encryptionKey))
        {
            // Double check the encryption key is saved
            PlayerPrefs.SetString("TempEncryptionKey", encryptionKey);
            Debug.Log("Encryption key saved to PlayerPrefs, length: " + encryptionKey.Length);
        }

        // Log all the relevant data before scene transition
        Debug.Log("Before scene transition:");
        Debug.Log("TempEncryptionKey exists: " + PlayerPrefs.HasKey("TempEncryptionKey"));
        Debug.Log("EncryptedRaceToken exists: " + PlayerPrefs.HasKey("EncryptedRaceToken"));
        Debug.Log("RaceToken exists: " + PlayerPrefs.HasKey("RaceToken"));
        Debug.Log("SelectedStakeAmount exists: " + PlayerPrefs.HasKey("SelectedStakeAmount"));
        Debug.Log("SelectedStakeAmount value: " + PlayerPrefs.GetFloat("SelectedStakeAmount", 0));

        // Force save all PlayerPrefs
        PlayerPrefs.Save();

        SceneManager.LoadScene(raceSceneName);
    }

    private void UpdateStakeButtonStates(float balance)
    {
        // Check if we have an unused stake
        if (selectedStakeAmount > 0 && !string.IsNullOrEmpty(PlayerPrefs.GetString("RaceToken", "")))
        {
            // We have an unused stake, disable only the specific stake button
            DisableSpecificStakeButton(selectedStakeAmount);

            // Enable other buttons based on balance
            if (stake5Button != null && selectedStakeAmount != 5)
                stake5Button.interactable = (balance >= 5);
            if (stake10Button != null && selectedStakeAmount != 10)
                stake10Button.interactable = (balance >= 10);
            if (stake20Button != null && selectedStakeAmount != 20)
                stake20Button.interactable = (balance >= 20);
            if (stake50Button != null && selectedStakeAmount != 50)
                stake50Button.interactable = (balance >= 50);
            return;
        }

        // No unused stake, enable all buttons based on balance
        if (stake5Button != null)
            stake5Button.interactable = (balance >= 5);

        if (stake10Button != null)
            stake10Button.interactable = (balance >= 10);

        if (stake20Button != null)
            stake20Button.interactable = (balance >= 20);

        if (stake50Button != null)
            stake50Button.interactable = (balance >= 50);
    }

    private string EnsureJsonWrapper(string json)
    {
        if (!string.IsNullOrEmpty(json) && !json.TrimStart().StartsWith("{"))
        {
            return "{\"rawResponse\":\"" + json.Replace("\"", "\\\"") + "\"}";
        }
        return json;
    }

    // Methods for security enhancement

    // رمزنگاری یک رشته
    private string EncryptString(string plainText)
    {
        try
        {
            if (string.IsNullOrEmpty(plainText) || string.IsNullOrEmpty(encryptionKey))
                return plainText;

            byte[] iv = new byte[16];
            byte[] array;

            using (Aes aes = Aes.Create())
            {
                aes.Key = Encoding.UTF8.GetBytes(encryptionKey);
                aes.IV = iv;

                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                using (var memoryStream = new System.IO.MemoryStream())
                {
                    using (var cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                    {
                        using (var streamWriter = new System.IO.StreamWriter(cryptoStream))
                        {
                            streamWriter.Write(plainText);
                        }
                        array = memoryStream.ToArray();
                    }
                }
            }
            return Convert.ToBase64String(array);
        }
        catch (Exception ex)
        {
            Debug.LogError($"Encryption error: {ex.Message}");
            return "";
        }
    }

    // رمزگشایی یک رشته
    private string DecryptString(string cipherText)
    {
        try
        {
            if (string.IsNullOrEmpty(cipherText) || string.IsNullOrEmpty(encryptionKey))
                return cipherText;

            byte[] iv = new byte[16];
            byte[] buffer = Convert.FromBase64String(cipherText);

            using (Aes aes = Aes.Create())
            {
                aes.Key = Encoding.UTF8.GetBytes(encryptionKey);
                aes.IV = iv;
                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                using (var memoryStream = new System.IO.MemoryStream(buffer))
                {
                    using (var cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read))
                    {
                        using (var streamReader = new System.IO.StreamReader(cryptoStream))
                        {
                            return streamReader.ReadToEnd();
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Decryption error: {ex.Message}");
            return "";
        }
    }

    // ذخیره‌سازی امن داده‌های حساس
    private void SecurePlayerPrefsSet(string key, string value)
    {
        var encryptedValue = EncryptString(value);
        PlayerPrefs.SetString(key, encryptedValue);
    }

    // دریافت امن داده‌های ذخیره شده
    private string SecurePlayerPrefsGet(string key)
    {
        var encryptedValue = PlayerPrefs.GetString(key, "");
        if (string.IsNullOrEmpty(encryptedValue) || string.IsNullOrEmpty(encryptionKey))
            return "";

        return DecryptString(encryptedValue);
    }

    private IEnumerator ChooseStakeCoroutine(float stakeAmount)
    {
        // Use the serialized apiUrl field rather than hardcoding the URL
        string url = apiUrl;
        string token = SessionManager.Instance.JWT_Token;
        string key1 = SessionManager.Instance.Key1;
        string nonce = SessionManager.Instance.CurrentNonce;

        // With 24-hour token system, we only need token and key1
        if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(key1))
        {
            Debug.LogError("Missing authentication data for stake request");
            if (statusText != null)
                statusText.text = "Error: Authentication data missing";
            yield break;
        }

        Dictionary<string, object> requestData = new Dictionary<string, object>
        {
            { "operation", "ChooseStake" },
            { "stake_amount", stakeAmount },
            { "key1", key1 }
        };

        // With 24-hour token system, nonce is optional
        if (!string.IsNullOrEmpty(nonce))
        {
            requestData.Add("nonce", nonce);
            Debug.Log($"Including nonce in request: {nonce}");
        }
        else
        {
            Debug.Log("No nonce available, but using 24-hour token system");
        }

        string jsonData = JsonConvert.SerializeObject(requestData);
        Debug.Log("Sending request to choose_stake.php with URL: " + url);
        Debug.Log("Sending request to choose_stake.php with data: " + jsonData);
        Debug.Log("Authorization Header: Bearer " + token);

        using (var request = new UnityWebRequest(url, "POST"))
        {
            byte[] bodyRaw = System.Text.Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            request.SetRequestHeader("Authorization", "Bearer " + token);

            if (statusText != null)
                statusText.text = "Sending request...";

            yield return request.SendWebRequest();

            if (request.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError("Request error: " + request.error + " - " + request.downloadHandler.text);
                if (statusText != null)
                    statusText.text = "Error: " + request.error;
                yield break;
            }

            string responseText = request.downloadHandler.text;
            Debug.Log("Response: " + responseText);

            // Process and update UI based on response
            if (string.IsNullOrEmpty(responseText))
            {
                Debug.LogError("Empty response from server");
                if (statusText != null)
                    statusText.text = "Error: Empty response from server";
                yield break;
            }

            ProcessStakeResponse(responseText);
        }
    }
}