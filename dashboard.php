<?php
// تنظیم نمایش خطاها برای دیباگ
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Handle AJAX requests for stake cleanup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax_action']) && $_POST['ajax_action'] === 'cleanup_stakes') {
    header('Content-Type: application/json');

    try {
        require_once '../includes/config.php';

        // Check database connection
        if (!isset($conn) || $conn->connect_error) {
            throw new Exception('Database connection failed');
        }

        $used_type = (int)$_POST['used_type'];

        if (!in_array($used_type, [2, 3])) {
            throw new Exception('Invalid used_type. Only 2 (replaced) and 3 (expired) are allowed.');
        }

        // Perform cleanup based on type
        $stmt = $conn->prepare("DELETE FROM race_tokens WHERE used = ?");
        if (!$stmt) {
            throw new Exception('Failed to prepare statement: ' . $conn->error);
        }

        $stmt->bind_param("i", $used_type);
        $result = $stmt->execute();

        if (!$result) {
            throw new Exception('Failed to execute statement: ' . $stmt->error);
        }

        $deleted_count = $stmt->affected_rows;

        // Log the action
        $type_name = ($used_type === 2) ? 'replaced' : 'expired';
        error_log("Admin cleanup: Deleted $deleted_count $type_name stakes (used = $used_type)");

        // Return success response
        echo json_encode([
            'success' => true,
            'deleted_count' => $deleted_count,
            'message' => "Successfully deleted $deleted_count stakes"
        ]);

    } catch (Exception $e) {
        // Return error response
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit; // Important: exit after AJAX response
}

// تنظیم عنوان صفحه
$title = 'داشبورد مدیریت';
$page_title = 'داشبورد مدیریت';

// تابع مدیریت خطاهای دیتابیس
function db_query($conn, $sql, $default_value = null) {
    $result = $conn->query($sql);
    if ($result === false) {
        echo "خطای SQL: " . $conn->error . "<br>Query: " . $sql . "<br>";
        return $default_value;
    }
    return $result;
}

try {
    // لود کردن هدر
    require_once 'header.php';
    
    // اضافه کردن استایل برای کلاس bg-purple
    echo '<style>
    .bg-purple {
        background-color: #6f42c1;
    }
    </style>';
    
    // بررسی اتصال به دیتابیس
    if (!isset($conn) || $conn->connect_error) {
        echo "<div class='alert alert-danger'>خطا در اتصال به پایگاه داده: " . ($conn->connect_error ?? "اتصال برقرار نشده است") . "</div>";
    }

    // دریافت آمارهای اصلی
    $stats = [];

    // تعداد کل کاربران
    $result = db_query($conn, "SELECT COUNT(*) as total FROM users");
    $stats['total_users'] = ($result && $result->num_rows > 0) ? $result->fetch_assoc()['total'] : 0;

    // مجموع واریزی‌ها
    $result = db_query($conn, "SELECT SUM(amount) as total FROM deposits WHERE status = 'completed'");
    $stats['total_deposits'] = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

    // مجموع برداشت‌ها
    $result = db_query($conn, "SELECT SUM(amount) as total FROM transactions WHERE (type = 'withdraw' OR type = 'withdrawal') AND status = 'completed'");
    $stats['total_withdrawals'] = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

    // تعداد کل تراکنش‌ها
    $result = db_query($conn, "SELECT COUNT(*) as total FROM transactions");
    $stats['total_transactions'] = ($result && $result->num_rows > 0) ? $result->fetch_assoc()['total'] : 0;

    // دریافت آمارهای تکمیلی
    // تعداد کاربران امروز
    $result = db_query($conn, "SELECT COUNT(*) as count FROM users WHERE DATE(updated_at) = CURDATE()");
    $new_users_today = ($result && $result->num_rows > 0) ? $result->fetch_assoc()['count'] : 0;

    // مبلغ تراکنش‌های امروز
    $result = db_query($conn, "SELECT SUM(amount) as total FROM transactions WHERE DATE(updated_at) = CURDATE() AND status = 'completed'");
    $today_transaction_amount = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

    // تعداد کل بازی‌های انجام شده
    $result = db_query($conn, "SELECT COUNT(*) as total FROM game_stats");
    $total_games_played = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

    // تعداد بازی‌های امروز
    $result = db_query($conn, "SELECT COUNT(*) as total FROM game_stats WHERE DATE(updated_at) = CURDATE()");
    $games_played_today = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

    // دریافت سود خالص سیستم (تفاوت بین واریزی‌ها و برداشت‌ها)
    $result = db_query($conn, "SELECT 
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'completed') -
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE (type = 'withdraw' OR type = 'withdrawal') AND status = 'completed') as net_profit");
    $net_profit = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['net_profit'] ?? 0) : 0;

    // دریافت کاربران اخیر
    $recent_users = db_query($conn, "SELECT id, username, email, updated_at, win_balance FROM users ORDER BY id DESC LIMIT 5");

    // دریافت تراکنش‌های اخیر
    $recent_transactions = db_query($conn, "SELECT t.id, t.user_id, t.type, t.amount, t.status, t.updated_at, u.username 
            FROM transactions t 
            JOIN users u ON t.user_id = u.id 
            ORDER BY t.id DESC LIMIT 10");

    // تعداد درخواست‌های برداشت در انتظار
    $result = db_query($conn, "SELECT COUNT(*) as count FROM transactions WHERE (type = 'withdraw' OR type = 'withdrawal') AND status = 'pending'");
    $pending_withdrawals = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['count'] ?? 0) : 0;

    // کل مبلغ برداشت‌های در انتظار
    $result = db_query($conn, "SELECT SUM(amount) as total FROM transactions WHERE (type = 'withdraw' OR type = 'withdrawal') AND status = 'pending'");
    $pending_withdrawals_amount = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

    // دریافت مجموع کارمزد از جدول admins
    $result = db_query($conn, "SELECT total_fee FROM admins WHERE role = 'super_admin' LIMIT 1");
    $total_admin_fee = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total_fee'] ?? 0) : 0;

    // آمار استیک‌ها با سیستم جدید
    // استیک‌های استفاده شده در بازی (used = 1)
    $result = db_query($conn, "SELECT COUNT(*) as used FROM race_tokens WHERE used = 1");
    $used_stakes = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['used'] ?? 0) : 0;

    // استیک‌های جایگذین شده (used = 2)
    $result = db_query($conn, "SELECT COUNT(*) as replaced FROM race_tokens WHERE used = 2");
    $replaced_stakes_count = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['replaced'] ?? 0) : 0;

    $result = db_query($conn, "SELECT SUM(stake_amount) as total FROM race_tokens WHERE used = 2");
    $replaced_stakes_amount = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

    // استیک‌های منقضی شده (used = 3)
    $result = db_query($conn, "SELECT COUNT(*) as expired FROM race_tokens WHERE used = 3");
    $expired_stakes_count = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['expired'] ?? 0) : 0;

    $result = db_query($conn, "SELECT SUM(stake_amount) as total FROM race_tokens WHERE used = 3");
    $expired_stakes_amount = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

    // استیک‌های فعال (used = 0)
    $result = db_query($conn, "SELECT COUNT(*) as unused FROM race_tokens WHERE used = 0");
    $active_unused_stakes = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['unused'] ?? 0) : 0;

    // کل استیک‌ها
    $result = db_query($conn, "SELECT COUNT(*) as total FROM race_tokens");
    $total_stakes = ($result && $result->num_rows > 0) ? ($result->fetch_assoc()['total'] ?? 0) : 0;

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطایی رخ داده است: " . $e->getMessage() . "</div>";
}
?>

<div class="row mb-4">
    <div class="col">
        <h1 class="h2 mb-0">داشبورد</h1>
        <p class="text-muted">خلاصه وضعیت سیستم و آمارهای کلی</p>
    </div>
</div>

<!-- آمارهای کلی -->
<div class="row mb-4">
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">کاربران</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($stats['total_users']); ?></h2>
                        <p class="mb-0"><small>کاربر امروز: <?php echo $new_users_today; ?></small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">بازی‌ها</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($total_games_played); ?></h2>
                        <p class="mb-0"><small>امروز: <?php echo number_format($games_played_today); ?></small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-controller"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">واریزی‌ها</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($stats['total_deposits'], 2); ?></h2>
                        <p class="mb-0"><small>USDT</small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-wallet2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">برداشت‌ها</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($stats['total_withdrawals'], 2); ?></h2>
                        <p class="mb-0"><small>USDT</small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-cash-stack"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آمارهای اضافی -->
<div class="row mb-4">
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-warning text-dark h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">تراکنش‌های امروز</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($today_transaction_amount, 2); ?></h2>
                        <p class="mb-0"><small>USDT</small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-arrow-left-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-secondary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">برداشت‌های در انتظار</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($pending_withdrawals); ?></h2>
                        <p class="mb-0"><small>مبلغ: <?php echo number_format($pending_withdrawals_amount, 2); ?> USDT</small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-hourglass-split"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-dark text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">کل تراکنش‌ها</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($stats['total_transactions']); ?></h2>
                        <p class="mb-0"><small>تعداد</small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-credit-card"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card <?php echo $net_profit >= 0 ? 'bg-success' : 'bg-danger'; ?> text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">سود خالص سیستم</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($net_profit, 2); ?></h2>
                        <p class="mb-0"><small>USDT</small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-graph-up-arrow"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آمار کارمزد و استیک‌ها -->
<div class="row mb-4">
    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-purple text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">مجموع کارمزد</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($total_admin_fee, 2); ?></h2>
                        <p class="mb-0"><small>USDT</small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-cash-coin"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-warning text-dark h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">استیک‌های جایگذین</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($replaced_stakes_count); ?></h2>
                        <p class="mb-0"><small>مبلغ: $<?php echo number_format($replaced_stakes_amount, 2); ?></small></p>
                        <button class="btn btn-sm btn-outline-dark mt-1" onclick="cleanupStakes(2)">پاک کردن</button>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-arrow-repeat"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">استیک‌های منقضی</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($expired_stakes_count); ?></h2>
                        <p class="mb-0"><small>مبلغ: $<?php echo number_format($expired_stakes_amount, 2); ?></small></p>
                        <button class="btn btn-sm btn-outline-light mt-1" onclick="cleanupStakes(3)">پاک کردن</button>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-fire"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">استیک‌های فعال</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($active_unused_stakes); ?></h2>
                        <p class="mb-0"><small>استفاده نشده</small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-hourglass-split"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-lg-3 mb-4">
        <div class="card stat-card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">کل استیک‌ها</h6>
                        <h2 class="mt-2 mb-0"><?php echo number_format($total_stakes); ?></h2>
                        <p class="mb-0"><small>استفاده شده: <?php echo number_format($used_stakes); ?></small></p>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-trophy"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- بخش مدیریت استیک‌ها -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">مدیریت استیک‌ها</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>آمار تفصیلی:</h6>
                        <ul class="list-unstyled">
                            <li><strong>استیک‌های فعال (used = 0):</strong> <?php echo number_format($active_unused_stakes); ?></li>
                            <li><strong>استیک‌های استفاده شده (used = 1):</strong> <?php echo number_format($used_stakes); ?></li>
                            <li><strong>استیک‌های جایگذین شده (used = 2):</strong> <?php echo number_format($replaced_stakes_count); ?> - مبلغ: $<?php echo number_format($replaced_stakes_amount, 2); ?></li>
                            <li><strong>استیک‌های منقضی شده (used = 3):</strong> <?php echo number_format($expired_stakes_count); ?> - مبلغ: $<?php echo number_format($expired_stakes_amount, 2); ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>عملیات پاکسازی:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="cleanupStakes(2)">
                                <i class="bi bi-trash"></i> پاک کردن استیک‌های جایگذین شده (<?php echo number_format($replaced_stakes_count); ?>)
                            </button>
                            <button class="btn btn-danger" onclick="cleanupStakes(3)">
                                <i class="bi bi-fire"></i> پاک کردن استیک‌های منقضی شده (<?php echo number_format($expired_stakes_count); ?>)
                            </button>
                            <a href="cleanup_expired_stakes.php" class="btn btn-secondary" target="_blank">
                                <i class="bi bi-gear"></i> اجرای اسکریپت پاکسازی کامل
                            </a>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            نکته: استیک‌های استفاده شده (used = 1) فقط از طریق اسکریپت پاکسازی کامل حذف می‌شوند.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- درخواست‌های برداشت در انتظار -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">درخواست‌های برداشت در انتظار</h5>
                <a href="withdrawals.php" class="btn btn-sm btn-primary">مشاهده همه</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">کاربر</th>
                                <th scope="col">مبلغ</th>
                                <th scope="col">تاریخ</th>
                                <th scope="col">عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $result = db_query($conn, "SELECT t.id, t.user_id, t.amount, t.updated_at, u.username 
                                    FROM transactions t 
                                    JOIN users u ON t.user_id = u.id 
                                    WHERE (t.type = 'withdraw' OR t.type = 'withdrawal') AND t.status = 'pending' 
                                    ORDER BY t.updated_at DESC 
                                    LIMIT 5");
                            
                            if ($result && $result->num_rows > 0) {
                                while ($row = $result->fetch_assoc()) {
                                    echo "<tr>
                                            <td>{$row['id']}</td>
                                            <td>{$row['username']}</td>
                                            <td>" . number_format($row['amount'], 2) . " USDT</td>
                                            <td>" . date('Y/m/d H:i', strtotime($row['updated_at'])) . "</td>
                                            <td>
                                                <a href='withdrawals.php?id={$row['id']}' class='btn btn-sm btn-outline-primary'>بررسی</a>
                                            </td>
                                        </tr>";
                                }
                            } else {
                                echo "<tr><td colspan='5' class='text-center'>درخواست برداشتی در انتظار وجود ندارد.</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- کاربران جدید -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">کاربران جدید</h5>
                <a href="users.php" class="btn btn-sm btn-primary">مشاهده همه</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">نام کاربری</th>
                                <th scope="col">ایمیل</th>
                                <th scope="col">موجودی</th>
                                <th scope="col">تاریخ ثبت‌نام</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($recent_users->num_rows > 0) {
                                while ($user = $recent_users->fetch_assoc()) {
                                    echo "<tr>
                                            <td>{$user['id']}</td>
                                            <td>{$user['username']}</td>
                                            <td>{$user['email']}</td>
                                            <td>" . number_format($user['win_balance'], 2) . " USDT</td>
                                            <td>" . date('Y/m/d', strtotime($user['updated_at'])) . "</td>
                                        </tr>";
                                }
                            } else {
                                echo "<tr><td colspan='5' class='text-center'>هیچ کاربری یافت نشد.</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تراکنش‌های اخیر -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تراکنش‌های اخیر</h5>
                <a href="transactions.php" class="btn btn-sm btn-primary">مشاهده همه</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">کاربر</th>
                                <th scope="col">نوع</th>
                                <th scope="col">مبلغ</th>
                                <th scope="col">وضعیت</th>
                                <th scope="col">تاریخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($recent_transactions->num_rows > 0) {
                                while ($tx = $recent_transactions->fetch_assoc()) {
                                    // نوع تراکنش
                                    $type_text = ($tx['type'] == 'deposit') ? 'واریز' : 'برداشت';
                                    $type_class = ($tx['type'] == 'deposit') ? 'success' : 'info';
                                    
                                    // وضعیت تراکنش
                                    $status_text = '';
                                    $status_class = '';
                                    switch ($tx['status']) {
                                        case 'pending':
                                            $status_text = 'در انتظار تایید';
                                            $status_class = 'warning';
                                            break;
                                        case 'processing':
                                            $status_text = 'در حال پردازش';
                                            $status_class = 'info';
                                            break;
                                        case 'completed':
                                            $status_text = 'تکمیل شده';
                                            $status_class = 'success';
                                            break;
                                        case 'failed':
                                            $status_text = 'ناموفق';
                                            $status_class = 'danger';
                                            break;
                                        default:
                                            $status_text = $tx['status'];
                                            $status_class = 'secondary';
                                    }
                                    
                                    echo "<tr>
                                            <td>{$tx['id']}</td>
                                            <td>{$tx['username']}</td>
                                            <td><span class='badge bg-$type_class'>$type_text</span></td>
                                            <td>" . number_format($tx['amount'], 2) . " USDT</td>
                                            <td><span class='badge bg-$status_class'>$status_text</span></td>
                                            <td>" . date('Y/m/d H:i', strtotime($tx['updated_at'])) . "</td>
                                        </tr>";
                                }
                            } else {
                                echo "<tr><td colspan='6' class='text-center'>هیچ تراکنشی یافت نشد.</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function cleanupStakes(usedType) {
    let typeName = '';
    let confirmMessage = '';

    if (usedType === 2) {
        typeName = 'جایگذین شده';
        confirmMessage = 'آیا مطمئن هستید که می‌خواهید تمام استیک‌های جایگذین شده (used = 2) را حذف کنید؟';
    } else if (usedType === 3) {
        typeName = 'منقضی شده';
        confirmMessage = 'آیا مطمئن هستید که می‌خواهید تمام استیک‌های منقضی شده (used = 3) را حذف کنید؟';
    }

    if (confirm(confirmMessage)) {
        // Show loading
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'در حال حذف...';
        button.disabled = true;

        // Send AJAX request to same file
        const formData = new FormData();
        formData.append('ajax_action', 'cleanup_stakes');
        formData.append('used_type', usedType);

        fetch('dashboard.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`موفقیت: ${data.deleted_count} استیک ${typeName} حذف شد.`);
                location.reload(); // Refresh page to update stats
            } else {
                alert('خطا: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور');
        })
        .finally(() => {
            button.textContent = originalText;
            button.disabled = false;
        });
    }
}
</script>

<?php
// لود کردن فوتر
require_once 'footer.php';
?>