<?php
require_once '../includes/config.php';
date_default_timezone_set('Asia/Tehran');

$is_browser = !empty($_SERVER['HTTP_HOST']);

if ($is_browser) {
    echo "<!DOCTYPE html><html><head><title>Cleanup</title><meta charset='utf-8'>
    <style>body{font-family:monospace;background:#f5f5f5;padding:20px;}
    .container{background:white;padding:20px;border-radius:5px;max-width:600px;margin:0 auto;}
    .success{color:green;}.error{color:red;}.info{color:blue;}
    </style></head><body><div class='container'><h2>Stake Cleanup</h2>";

    function output($text, $class = '') {
        echo "<div class='$class'>" . htmlspecialchars($text) . "</div>";
    }
} else {
    function output($text, $class = '') {
        echo $text . "\n";
    }
}

output("Started: " . date('Y-m-d H:i:s'), "info");

// Test database connection
if (!isset($conn)) {
    output("ERROR: Database connection not found", "error");
    if ($is_browser) {
        echo "</div></body></html>";
    }
    exit;
}

if ($conn->connect_error) {
    output("ERROR: Database connection failed: " . $conn->connect_error, "error");
    if ($is_browser) {
        echo "</div></body></html>";
    }
    exit;
}

output("Database connection: OK", "success");

// Set database timezone to match server timezone
$conn->query("SET time_zone = '+03:30'"); // Tehran timezone
output("Database timezone set to Tehran (+03:30)", "success");

// Show current database time vs server time
$result = $conn->query("SELECT NOW() as db_time");
$db_time = $result->fetch_assoc()['db_time'];
$server_time = date('Y-m-d H:i:s');
output("Server time: $server_time", "info");
output("Database time: $db_time", "info");

// Check if race_tokens table exists
$result = $conn->query("SHOW TABLES LIKE 'race_tokens'");
if ($result->num_rows === 0) {
    output("ERROR: race_tokens table not found", "error");
    if ($is_browser) {
        echo "</div></body></html>";
    }
    exit;
}

output("Table race_tokens: Found", "success");

// Show current data before cleanup
$result = $conn->query("SELECT COUNT(*) as total FROM race_tokens");
$total = $result ? $result->fetch_assoc()['total'] : 0;
output("Total stakes in database: $total", "info");

$result = $conn->query("SELECT COUNT(*) as count FROM race_tokens WHERE used = 0 AND expires_at < NOW()");
$expired_unused = $result ? $result->fetch_assoc()['count'] : 0;
output("Expired unused stakes (will convert to used=3): $expired_unused", "info");

$result = $conn->query("SELECT COUNT(*) as count FROM race_tokens WHERE used = 1 AND expires_at < NOW()");
$expired_used = $result ? $result->fetch_assoc()['count'] : 0;
output("Expired used stakes (will delete): $expired_used", "info");

// Show detailed info about all stakes
output("--- Detailed Stakes Info ---", "info");
$result = $conn->query("SELECT id, user_id, stake_amount, used, created_at, expires_at,
                        CASE WHEN expires_at < NOW() THEN 'EXPIRED' ELSE 'VALID' END as status
                        FROM race_tokens ORDER BY created_at DESC LIMIT 5");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $status_class = ($row['status'] == 'EXPIRED') ? 'error' : 'success';
        output("ID:{$row['id']} User:{$row['user_id']} Amount:{$row['stake_amount']} Used:{$row['used']} Status:{$row['status']}", $status_class);
        output("  Created: {$row['created_at']} | Expires: {$row['expires_at']}", "info");
    }
} else {
    output("No stakes found in database", "error");
}

try {
    // Convert expired unused stakes (used = 0) to expired status (used = 3)
    $stmt = $conn->prepare("UPDATE race_tokens SET used = 3 WHERE used = 0 AND expires_at < NOW()");
    $stmt->execute();
    $converted_count = $stmt->affected_rows;

    // Delete expired used stakes (used = 1)
    $stmt = $conn->prepare("DELETE FROM race_tokens WHERE used = 1 AND expires_at < NOW()");
    $stmt->execute();
    $deleted_count = $stmt->affected_rows;

    output("Converted: $converted_count unused stakes to expired", "success");
    output("Deleted: $deleted_count expired used stakes", "success");
    output("Total operations: " . ($converted_count + $deleted_count), "info");
    
    output("Cleanup completed successfully!", "success");

} catch (Exception $e) {
    output("ERROR: " . $e->getMessage(), "error");
}

output("Finished: " . date('Y-m-d H:i:s'), "info");

if ($is_browser) {
    echo "<br><a href='../dashboard.php'>← Back to Dashboard</a>";
    echo "</div></body></html>";
}
?>
