<?php
/**
 * Database cleanup script for expired and unused stakes
 * This script should be run manually or via cron job to clean up expired stakes
 */

require_once 'inc/config.php';

// Set timezone
date_default_timezone_set('Asia/Tehran');

echo "=== Stake Cleanup Script ===\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Get statistics before cleanup
    echo "=== BEFORE CLEANUP ===\n";
    
    // Total stakes
    $result = $conn->query("SELECT COUNT(*) as total FROM race_tokens");
    $total_stakes = $result->fetch_assoc()['total'];
    echo "Total stakes in database: $total_stakes\n";
    
    // Used stakes
    $result = $conn->query("SELECT COUNT(*) as used FROM race_tokens WHERE used = 1");
    $used_stakes = $result->fetch_assoc()['used'];
    echo "Used stakes: $used_stakes\n";
    
    // Unused stakes
    $result = $conn->query("SELECT COUNT(*) as unused FROM race_tokens WHERE used = 0");
    $unused_stakes = $result->fetch_assoc()['unused'];
    echo "Unused stakes: $unused_stakes\n";
    
    // Expired stakes (both used and unused)
    $result = $conn->query("SELECT COUNT(*) as expired FROM race_tokens WHERE expires_at < NOW()");
    $expired_stakes = $result->fetch_assoc()['expired'];
    echo "Expired stakes: $expired_stakes\n";
    
    // Expired unused stakes (these will be deleted)
    $result = $conn->query("SELECT COUNT(*) as expired_unused FROM race_tokens WHERE expires_at < NOW() AND used = 0");
    $expired_unused = $result->fetch_assoc()['expired_unused'];
    echo "Expired unused stakes (to be deleted): $expired_unused\n";
    
    // Calculate total value of expired unused stakes
    $result = $conn->query("SELECT SUM(stake_amount) as total_wasted FROM race_tokens WHERE expires_at < NOW() AND used = 0");
    $total_wasted = $result->fetch_assoc()['total_wasted'] ?? 0;
    echo "Total value of expired unused stakes: $" . number_format($total_wasted, 2) . "\n\n";
    
    // Show some examples of expired unused stakes
    echo "=== EXPIRED UNUSED STAKES (Examples) ===\n";
    $result = $conn->query("
        SELECT rt.user_id, u.username, rt.stake_amount, rt.created_at, rt.expires_at 
        FROM race_tokens rt 
        LEFT JOIN users u ON rt.user_id = u.id 
        WHERE rt.expires_at < NOW() AND rt.used = 0 
        ORDER BY rt.created_at DESC 
        LIMIT 10
    ");
    
    if ($result->num_rows > 0) {
        printf("%-8s %-15s %-12s %-20s %-20s\n", "User ID", "Username", "Amount", "Created", "Expired");
        echo str_repeat("-", 80) . "\n";
        
        while ($row = $result->fetch_assoc()) {
            printf("%-8s %-15s $%-11.2f %-20s %-20s\n", 
                $row['user_id'], 
                $row['username'] ?? 'Unknown', 
                $row['stake_amount'], 
                $row['created_at'], 
                $row['expires_at']
            );
        }
    } else {
        echo "No expired unused stakes found.\n";
    }
    
    echo "\n=== CLEANUP PROCESS ===\n";
    
    // Delete expired unused stakes
    $stmt = $conn->prepare("DELETE FROM race_tokens WHERE expires_at < NOW() AND used = 0");
    $stmt->execute();
    $deleted_count = $stmt->affected_rows;
    
    echo "Deleted $deleted_count expired unused stakes.\n";
    
    // Optionally, also clean up very old used stakes (older than 30 days)
    echo "\nCleaning up old used stakes (older than 30 days)...\n";
    $stmt = $conn->prepare("DELETE FROM race_tokens WHERE used = 1 AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $stmt->execute();
    $deleted_old = $stmt->affected_rows;
    
    echo "Deleted $deleted_old old used stakes.\n";
    
    echo "\n=== AFTER CLEANUP ===\n";
    
    // Get statistics after cleanup
    $result = $conn->query("SELECT COUNT(*) as total FROM race_tokens");
    $total_after = $result->fetch_assoc()['total'];
    echo "Total stakes remaining: $total_after\n";
    
    $result = $conn->query("SELECT COUNT(*) as used FROM race_tokens WHERE used = 1");
    $used_after = $result->fetch_assoc()['used'];
    echo "Used stakes remaining: $used_after\n";
    
    $result = $conn->query("SELECT COUNT(*) as unused FROM race_tokens WHERE used = 0");
    $unused_after = $result->fetch_assoc()['unused'];
    echo "Unused stakes remaining: $unused_after\n";
    
    echo "\n=== SUMMARY ===\n";
    echo "Expired unused stakes deleted: $deleted_count\n";
    echo "Old used stakes deleted: $deleted_old\n";
    echo "Total wasted amount: $" . number_format($total_wasted, 2) . "\n";
    echo "Database size reduced by: " . ($total_stakes - $total_after) . " records\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\nCompleted at: " . date('Y-m-d H:i:s') . "\n";
echo "=== END ===\n";
?>
