<?php
/**
 * Simple cleanup script for expired stakes
 * This script does two main tasks:
 * 1. Convert expired unused stakes (used = 0) to expired status (used = 3)
 * 2. Delete expired used stakes (used = 1) that have passed their expiry date
 */

require_once 'inc/config.php';

// Set timezone
date_default_timezone_set('Asia/Tehran');

echo "=== Simple Stake Cleanup Script ===\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Get statistics before cleanup
    echo "=== BEFORE CLEANUP ===\n";

    // Active unused stakes
    $result = $conn->query("SELECT COUNT(*) as unused FROM race_tokens WHERE used = 0");
    $unused_stakes = $result->fetch_assoc()['unused'];
    echo "Active unused stakes (used = 0): $unused_stakes\n";

    // Used stakes in games
    $result = $conn->query("SELECT COUNT(*) as used FROM race_tokens WHERE used = 1");
    $used_stakes = $result->fetch_assoc()['used'];
    echo "Used stakes in games (used = 1): $used_stakes\n";

    // Check how many will be affected
    $result = $conn->query("SELECT COUNT(*) as expired_unused FROM race_tokens WHERE used = 0 AND expires_at < NOW()");
    $expired_unused_count = $result->fetch_assoc()['expired_unused'];
    echo "Unused stakes that will be marked as expired: $expired_unused_count\n";

    $result = $conn->query("SELECT COUNT(*) as expired_used FROM race_tokens WHERE used = 1 AND expires_at < NOW()");
    $expired_used_count = $result->fetch_assoc()['expired_used'];
    echo "Used stakes that will be deleted: $expired_used_count\n\n";
    
    echo "=== CLEANUP PROCESS ===\n";

    // Task 1: Convert expired unused stakes (used = 0) to expired status (used = 3)
    echo "1. Converting expired unused stakes to expired status...\n";
    $stmt = $conn->prepare("UPDATE race_tokens SET used = 3 WHERE used = 0 AND expires_at < NOW()");
    $stmt->execute();
    $converted_count = $stmt->affected_rows;
    echo "   Converted $converted_count unused stakes to expired status (used = 3)\n";

    // Task 2: Delete expired used stakes (used = 1)
    echo "2. Deleting expired used stakes...\n";
    $stmt = $conn->prepare("DELETE FROM race_tokens WHERE used = 1 AND expires_at < NOW()");
    $stmt->execute();
    $deleted_count = $stmt->affected_rows;
    echo "   Deleted $deleted_count expired used stakes (used = 1)\n";
    
    echo "\n=== AFTER CLEANUP ===\n";

    // Get statistics after cleanup
    $result = $conn->query("SELECT COUNT(*) as unused FROM race_tokens WHERE used = 0");
    $unused_after = $result->fetch_assoc()['unused'];
    echo "Active unused stakes (used = 0): $unused_after\n";

    $result = $conn->query("SELECT COUNT(*) as used FROM race_tokens WHERE used = 1");
    $used_after = $result->fetch_assoc()['used'];
    echo "Used stakes (used = 1): $used_after\n";

    $result = $conn->query("SELECT COUNT(*) as replaced FROM race_tokens WHERE used = 2");
    $replaced_after = $result->fetch_assoc()['replaced'];
    echo "Replaced stakes (used = 2): $replaced_after\n";

    $result = $conn->query("SELECT COUNT(*) as expired FROM race_tokens WHERE used = 3");
    $expired_after = $result->fetch_assoc()['expired'];
    echo "Expired stakes (used = 3): $expired_after\n";

    $result = $conn->query("SELECT COUNT(*) as total FROM race_tokens");
    $total_after = $result->fetch_assoc()['total'];
    echo "Total stakes remaining: $total_after\n";

    echo "\n=== SUMMARY ===\n";
    echo "✓ Converted $converted_count unused stakes to expired status (used = 3)\n";
    echo "✓ Deleted $deleted_count expired used stakes (used = 1)\n";
    echo "✓ Total operations: " . ($converted_count + $deleted_count) . "\n";
    echo "✓ Database cleaned successfully!\n";

} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\nCompleted at: " . date('Y-m-d H:i:s') . "\n";
echo "=== END ===\n";
?>
