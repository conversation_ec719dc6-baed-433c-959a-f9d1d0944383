<?php
/**
 * AJAX endpoint for cleaning up stakes from dashboard
 */

header('Content-Type: application/json');
require_once 'inc/config.php';

// Check if user is admin (you should implement proper admin authentication)
// For now, we'll assume this is protected by your admin authentication system

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action']) || $input['action'] !== 'cleanup') {
        throw new Exception('Invalid request');
    }
    
    $used_type = (int)$input['used_type'];
    
    if (!in_array($used_type, [2, 3])) {
        throw new Exception('Invalid used_type. Only 2 (replaced) and 3 (expired) are allowed.');
    }
    
    // Perform cleanup based on type
    $stmt = $conn->prepare("DELETE FROM race_tokens WHERE used = ?");
    $stmt->bind_param("i", $used_type);
    $stmt->execute();
    
    $deleted_count = $stmt->affected_rows;
    
    // Log the action
    $type_name = ($used_type === 2) ? 'replaced' : 'expired';
    error_log("Admin cleanup: Deleted $deleted_count $type_name stakes (used = $used_type)");
    
    // Return success response
    echo json_encode([
        'success' => true,
        'deleted_count' => $deleted_count,
        'message' => "Successfully deleted $deleted_count stakes"
    ]);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
